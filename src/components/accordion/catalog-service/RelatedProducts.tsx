import React, { useEffect, useState } from 'react';
import { Button } from '../../UI-components/Button';
import {
  ColumnDef,
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { TableContainer, TableHolder } from '../../UI-components/Table';
import {
  ProductColumns,
  RelatedProductTableColumns as columns,
} from '../../table/product/Columns';
import { Spacer } from '../../UI-components/Grid';
import CategorySearchFilter from '../../modal/catalog-service/CategorySearchModal';
import axios from 'axios';
import { useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { DataTable } from '../../table/product/DataTable';
import NothingToshow from '../../UI-components/NothingToShow';
import routes from '../../../constants/routes';
import { baseTheme } from '../../../themes/theme';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const TableCard = styled.div`
  margin: 10px 0px;
`;
const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const RelatedProducts = () => {
  const [isSearch, setIsSearch] = useState(false);
  const [page, setPage] = useState(1);
  const [data, setData] = useState<ProductColumns[]>([]);
  const { contextProdData, setContextProdData, setContextUpdateProdData } =
    useProductContext();
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const addToast = useToast();

  const [relatedProd, setRelatedProd] = useState<number[]>([]);
  const relatedData = contextProdData.product_links?.related;

  useEffect(() => {
    if (relatedData && Array.isArray(relatedData)) {
      const productIds = relatedData.map((item) => item.product_id);
      setRelatedProd(productIds);
    } else {
      setRelatedProd([]);
    }
  }, [relatedData]);

  const { data: related, refetch } = useQuery({
    queryKey: ['get-related-prod'],
    queryFn: async () => {
      const postdata = {
        product_ids: relatedProd,
        filters: { showCategoryNames: true, showOptionsValues: true },
        pagination: {
          page: page,
        },
      };
      if (relatedProd && relatedProd.length > 0) {
        try {
          const response = await axios.post(
            `${krakendPaths.CATALOG_URL}/admin-api/v1/products/search`,
            // `${constants.CATALOG_URL}/v1/catalog-admin/search-products`,
            postdata,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem('api-token')}`,
                'x-api-key': `${constants.CATALOG_KEY}`,
                'Content-Type': 'application/json',
              },
            },
          );
          return response.data;
        } catch (error) {
          throw new Error('Failed to fetch Related Product');
        }
      } else {
        return {
          item_count: 0,
          items: [],
          page_no: 1,
          page_size: 1,
          pages_count: null,
        };
      }
    },
    onError: (err) => {
      // console.log('Related product error', err);
      addToast('error', `Related product error:  ${err}`);
    },
    onSuccess: (data) => {
      // console.log('related product data: ', data);
      setData(data.items);
    },
    enabled: relatedProd && relatedProd.length > 0,
  });

  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    state: {
      columnVisibility,
      rowSelection,
    },
  });
  useEffect(() => {
    table.setPageSize(100);
  }, []);

  useEffect(() => {
    refetch();
  }, [relatedProd, contextProdData]);

  return (
    <div
      style={{
        marginBottom: `${baseTheme.paddings.xl}`,
      }}
    >
      <p>
        Related products are shown to customers in addition to the item the
        customer is looking at.
      </p>
      <Button isPrimary isOrange onClick={() => setIsSearch(!isSearch)}>
        Add Related Products
      </Button>
      <Spacer />
      <Container>
        <TableCard>
          <TableHolder>
            {table.getRowModel()?.rows?.length ? (
              <>
                <DataTable
                  table={table}
                  columns={columns}
                  data={related?.items}
                />
              </>
            ) : (
              <NothingToshow divHeight="55vh" />
            )}
          </TableHolder>
        </TableCard>
      </Container>
      <div style={{ overflowX: 'auto' }}>
        {isSearch && (
          <CategorySearchFilter
            close={() => {
              setIsSearch(false);
            }}
            setProducts={(products) => {
              const relatedProducts = products.map((product, index) => ({
                product_id: product.id,
                position: 0,
              }));
              // console.log("relatedProducts: ",relatedProducts)

              setContextProdData((prevState) => ({
                ...prevState,
                product_links: {
                  ...prevState.product_links,
                  related: prevState.product_links?.related
                    ? [...prevState.product_links.related, ...relatedProducts]
                    : relatedProducts,
                },
              }));
              setContextUpdateProdData((prevState) => ({
                ...prevState,
                product_links: {
                  ...prevState.product_links,
                  related: prevState.product_links?.related
                    ? [...prevState.product_links.related, ...relatedProducts]
                    : relatedProducts,
                },
              }));
              // updateRelatedProd({id: contextProdData?.id, relId: products[0].id})
            }}
          />
        )}
      </div>
    </div>
  );
};

export default RelatedProducts;
