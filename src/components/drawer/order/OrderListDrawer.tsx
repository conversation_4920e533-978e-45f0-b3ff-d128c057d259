import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@zendeskgarden/react-buttons';
import { DrawerModal } from '@zendeskgarden/react-modals';
import Input from '../../UI-components/Input';
import {
  Field as DTField,
  Label,
  Input as DTInput,
} from '@zendeskgarden/react-forms';

import { Row } from '../../UI-components/Grid';
import { Col } from '../../UI-components/Grid';
import {
  Dropdown,
  Field as DField,
  Multiselect,
  Trigger,
  Label as DLabel,
  Select as DSelect,
  Autocomplete,
} from '@zendeskgarden/react-dropdowns';
import { Menu } from '@zendeskgarden/react-dropdowns';
import { Item, Field as DropDownField } from '@zendeskgarden/react-dropdowns';
import { ApprovedByOutput } from '../../../gql/graphql';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { Tag } from '@zendeskgarden/react-tags';
import { format } from 'date-fns';
import { Select } from '@zendeskgarden/react-forms';
import { IItem } from '../ViewCancelRequestDrawer';

const CountryCodes: IItem[] = [
  { label: 'In', value: 'IN' },
  { label: 'Not In', value: 'NOT IN' },
];
export interface OrderfilterObject {
  order_id?: string | undefined;
  payment_method_code?: string[] | undefined;
  source?: string[] | undefined;
  status?: string | undefined;
  erp_status?: string[] | undefined;
  entity_id?: string | undefined;
  customer_email?: string | undefined;
  country_id?: string | undefined;
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
  dateFilter?: any;
  page_size: number;
  page_no: number;
}

export const OrderListFilters = ({
  isOpen,
  setIsOpen,
  filters,
  setFilters,
  customer_id,
  setCustomer_id,
  order_status,
  setOrder_status,
  endDate,
  setEndDate,
  startDate,
  setStartDate,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  filters: OrderfilterObject;
  setFilters: React.Dispatch<React.SetStateAction<any>>;
  customer_id: string | undefined;
  setCustomer_id: React.Dispatch<React.SetStateAction<string | undefined>>;
  order_status: string;
  setOrder_status: React.Dispatch<React.SetStateAction<string>>;
  endDate: Date | undefined;
  setEndDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  startDate: Date | undefined;
  setStartDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
}) => {
  const close = () => setIsOpen(false);

  const dateFormatter = new Intl.DateTimeFormat('en-CA', {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
  });

  const applyFilters = () => {
    setFilters((prev: any) => ({
      ...prev,
      page_no: 1,
      status: order_status.length > 0 ? order_status : undefined,
      customer_id: customer_id ?? undefined,
      start_date: startDate?.toLocaleDateString('en-CA'),
      end_date: endDate?.toLocaleDateString('en-CA'),
    }));
    close();
  };

  return (
    <Row>
      <Col textAlign="center">
        <DrawerModal isOpen={isOpen} onClose={close}>
          <DrawerModal.Header tag="h2">Filters</DrawerModal.Header>
          <DrawerModal.Body>
            <Row justifyContent="center" alignItems="center">
              <Col size={12} mt="sm">
                <Dropdown
                  inputValue={order_status}
                  onSelect={(items) => setOrder_status(items)}
                  downshiftProps={{ defaultHighlightedIndex: 0 }}
                >
                  <DropDownField>
                    <Label>Order Status</Label>
                    <Autocomplete>{order_status}</Autocomplete>
                  </DropDownField>
                  <Menu>
                    <Item key={1} value={'Order Placed'}>
                      Order Placed
                    </Item>
                    <Item key={2} value={'Payment Pending'}>
                      Payment Pending
                    </Item>
                    <Item key={3} value={'Partially Packed'}>
                      Partially Packed
                    </Item>
                    <Item key={4} value={'Packed'}>
                      Packed
                    </Item>
                    <Item key={5} value={'Partially Delivered'}>
                      Partially Delivered
                    </Item>
                    <Item key={6} value={'Shipped'}>
                      Shipped
                    </Item>
                    <Item key={7} value={'Delivered'}>
                      Delivered
                    </Item>
                    <Item key={8} value={'Cancelled'}>
                      Cancelled
                    </Item>
                    <Item key={9} value={'Returned'}>
                      Returned
                    </Item>
                    <Item key={10} value={'Cancellation Requested'}>
                      Cancellation Requested
                    </Item>
                    <Item key={11} value={'Cancellation Failed'}>
                      Cancellation Failed
                    </Item>
                    <Item key={12} value={'Partially Shipped'}>
                      Partially Shipped
                    </Item>
                  </Menu>
                </Dropdown>
              </Col>

              <Col size={12} mt={'sm'}>
                <DTField>
                  <Label>Order From</Label>
                  <Datepicker
                    placement="bottom"
                    value={startDate}
                    onChange={setStartDate}
                    formatDate={(date) => dateFormatter.format(date)}
                    maxValue={endDate ? endDate : new Date()}
                  >
                    <DTInput />
                  </Datepicker>
                </DTField>
              </Col>
              <Col size={12} mt={'sm'}>
                <DTField>
                  <Label>Order To</Label>
                  <Datepicker
                    value={endDate}
                    onChange={setEndDate}
                    formatDate={(date) => dateFormatter.format(date)}
                    maxValue={new Date()}
                  >
                    <DTInput />
                  </Datepicker>
                </DTField>
              </Col>
              <Col size={12} mt={'sm'}>
                <Label>Customer Id</Label>
                <Input
                  value={customer_id}
                  onChange={(e) => {
                    setCustomer_id(e.target.value);
                  }}
                />
              </Col>
            </Row>
          </DrawerModal.Body>
          <DrawerModal.Footer>
            <DrawerModal.FooterItem>
              <Button
                isDanger
                isPrimary
                onClick={() => {
                  setCustomer_id(undefined);
                  setOrder_status('');
                  setEndDate(undefined);
                  setStartDate(undefined);
                  setFilters({
                    rowsPerPage: 20,
                    pageNumber: 1,
                  });
                }}
              >
                Reset Filters
              </Button>
            </DrawerModal.FooterItem>
            <DrawerModal.FooterItem>
              <Button isPrimary onClick={applyFilters}>
                Apply Filters
              </Button>
            </DrawerModal.FooterItem>
          </DrawerModal.Footer>
          <DrawerModal.Close />
        </DrawerModal>
      </Col>
    </Row>
  );
};
