import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Field,
  Menu,
  Item,
  Select,
  Label,
} from '@zendeskgarden/react-dropdowns';
import useAxios from '../../../hooks/useAxios';
import { IGQL, IResolution } from '../../../types/types';
import { baseTheme } from '../../../themes/theme';
import { useQuery } from '@tanstack/react-query';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const ReturnDTOStageDropdown = ({
  activeDTOStage,
  setActiveDTOStage,
  disable,
  isCreatePage,
}: {
  setActiveDTOStage: React.Dispatch<React.SetStateAction<string | undefined>>;
  disable?: boolean;
  activeDTOStage?: string;
  isCreatePage?: boolean;
}) => {
  const [selectedDTO, setSelectedDTO] = useState<string>(
    activeDTOStage as string,
  );
  const [dtoStages, setDTOStages] = useState<IGQL[]>([]);

  useEffect(() => {
    // setInspectionRemark(testData);
    if (selectedDTO) {
      setActiveDTOStage(selectedDTO);
    } else {
      setActiveDTOStage(undefined);
    }
  }, [selectedDTO]);

  useEffect(() => {
    if (activeDTOStage) {
      const selectedObject = dtoStages.find(
        (item) =>
          item.name.trim().toLowerCase() ===
          activeDTOStage.trim().toLowerCase(),
      );
      if (selectedObject) {
        setSelectedDTO(selectedObject?.name);
      }
    }
  }, [dtoStages]);

  const [filters, setFilters] = useState({
    rowsPerPage: 20,
    pageNumber: 1,
  });

  const axios = useAxios();

  const {
    data,
    isLoading: queryLoading,
    refetch,
  } = useQuery({
    queryKey: ['configList', 'return_dto_stage'],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/config`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
          params: { type: 'return_dto_stage' },
        },
      );

      return response;
    },
    staleTime: 0,
    cacheTime: 0,
    onSuccess: (data: any) => {
      let result: IGQL[] = data.getConfigList;
      // .filter(
      //   (item: IGQL) => item.name !== 'refund',
      // );
      if (isCreatePage) {
        result = result.filter(
          (r) => !['refund', 'repair_vendor'].includes(r.name),
        );
      }
      setDTOStages(result);
    },
    onError: (error: any) => {
      console.error(error);
    },
  });

  return (
    <>
      <Dropdown
        selectedItem={selectedDTO}
        onSelect={(item) => {
          setSelectedDTO(item?.name);
        }}
        downshiftProps={{
          itemToString: (item: IGQL) => item && item.name,
        }}
      >
        <Field>
          <Label
            style={{
              color: baseTheme.colors.veryDarkGray,
              fontWeight: baseTheme.fontWeights.regular,
            }}
          >
            DTO Stage
          </Label>
          <Select disabled={disable}>{selectedDTO || 'Select'}</Select>
        </Field>
        <Menu>
          {dtoStages
            .filter((option) => option.enable === true)
            .map((option) => (
              <Item key={option._id} value={option}>
                {option.name}
              </Item>
            ))}
          <Item key={'reset'} value={''}>
            {'RESET'}
          </Item>
        </Menu>
      </Dropdown>
    </>
  );
};

export default ReturnDTOStageDropdown;
