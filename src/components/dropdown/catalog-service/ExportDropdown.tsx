import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { Row, Col } from '@zendeskgarden/react-grid';
import { Button } from '../../UI-components/Button';
import { useMutation } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import axios from 'axios';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const DropdownItem = styled.div`
  max-height: 300px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(2, 2fr);
  gap: 10px;
  padding: 20px 15px;
  cursor: pointer;
`;

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const DropdownMenu = styled.ul<{ size?: string }>`
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 999;
  width: ${({ size }) => size || '200px'};
  padding: 0;
  margin: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  list-style-type: none;
`;

interface CheckboxProps {
  checked: boolean;
  onChange: () => void;
  label: string;
  identifier: string;
}

const CheckboxContainer = styled.label`
  display: flex;
  align-items: center;
`;

const CheckboxInput = styled.input`
  margin-right: 5px;
`;

const CheckboxLabel = styled.span<{ htmlFor: string }>``;

const CheckboxExport: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  label,
  identifier,
}) => (
  <CheckboxContainer>
    <CheckboxInput
      type="checkbox"
      checked={checked}
      onChange={onChange}
      id={identifier}
    />
    <CheckboxLabel htmlFor={identifier}>{label}</CheckboxLabel>
  </CheckboxContainer>
);

const ExportDropdown = ({
  isExport,
  setIsExport,
}: {
  isExport: boolean;
  setIsExport: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { columnList, setColumnList, filters } = useProductContext();
  const addToast = useToast();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [selectAll, setSelectAll] = useState(false);

  const checkboxes = [
    { label: 'Product Name', identifier: 'name' },
    { label: 'Product Type', identifier: 'product_type' },
    { label: 'SKU', identifier: 'sku' },
    { label: 'Backorders', identifier: 'backorders' },
    { label: 'Quantity', identifier: 'qty' },
    { label: 'Visibility', identifier: 'visibility' },
    { label: 'Status', identifier: 'status' },
    { label: 'Country of Origin', identifier: 'country_of_manufacture' },
    { label: 'HSN Code', identifier: 'hsn_code' },
    { label: 'Tax Class', identifier: 'tax_class_id' },
    { label: 'Request Price', identifier: 'msrp' },
    { label: 'Product Expiry Date', identifier: 'pd_expiry_date' },
    { label: 'International View', identifier: 'international_active' },
    { label: 'Short Description', identifier: 'short_description' },
    { label: 'Description', identifier: 'description' },
    { label: 'Features', identifier: 'features' },
    { label: 'Key Specifications', identifier: 'key_specifications' },
    { label: 'Packaging', identifier: 'packaging' },
    { label: 'Direction to use', identifier: 'htext' },
    { label: 'Warranty', identifier: 'warranty' },
    { label: 'Additional Info', identifier: 'other_info' },
    { label: 'Categories', identifier: 'categories' },
    { label: 'URL Key', identifier: 'url_key' },
    { label: 'Meta Title', identifier: 'meta_title' },
    { label: 'Meta Keyword', identifier: 'meta_keyword' },
    { label: 'Meta Description', identifier: 'meta_description' },
    { label: 'Manufacturer', identifier: 'manufacturer' },
    { label: 'Dispatch Days', identifier: 'dispatch_days' },
    { label: 'Price', identifier: 'price' },
    { label: 'Selling Price', identifier: 'special_price' },
    { label: 'Special from date', identifier: 'special_from_date' },
    { label: 'Special to date', identifier: 'special_to_date' },
    { label: 'Weight', identifier: 'weight' },
    { label: 'Reward Point', identifier: 'reward_point_product' },
  ];

  const { mutate, isLoading } = useMutation({
    mutationFn: async (): Promise<any> => {
      const appliedFilters = {
        id:
          filters?.id?.from || filters?.id?.to
            ? {
                from: filters.id.from,
                to: filters.id.to,
              }
            : undefined,
        name: filters.name || undefined,
        sku: filters.sku || undefined,
        product_expiry:
          filters?.product_expiry?.from || filters?.product_expiry?.to
            ? {
                from: filters?.product_expiry?.from,
                to: filters?.product_expiry?.to,
              }
            : undefined,
        manufacturer: filters.manufacturer || undefined,
        backorders: filters.backorders || undefined,
        is_in_stock: filters.is_in_stock || undefined,
        quantity:
          filters?.quantity?.min || filters?.quantity?.max
            ? {
                min: filters?.quantity?.min,
                max: filters.quantity?.max,
              }
            : undefined,
        status: filters.status || undefined,
        type_id: filters.type_id || undefined,
      };

      const postdata = Object.keys(appliedFilters).some(
        (key) =>
          appliedFilters[key as keyof typeof appliedFilters] !== undefined,
      )
        ? {
            filters: appliedFilters,
            columns_list: columnList,
          }
        : {
            product_ids: [],
            filters: {},
            columns_list:
              columnList.length === 0
                ? [
                    'name',
                    'product_type',
                    'sku',
                    'backorders',
                    'qty',
                    'visibility',
                    'status',
                    'country_of_manufacture',
                    'hsn_code',
                    'tax_class_id',
                    'msrp',
                    'pd_expiry_date',
                    'international_active',
                    'short_description',
                    'description',
                    'features',
                    'key_specifications',
                    'packaging',
                    'htext',
                    'warranty',
                    'other_info',
                    'categories',
                    'url_key',
                    'meta_title',
                    'meta_keyword',
                    'meta_description',
                    'manufacturer',
                    'dispatch_days',
                    'price',
                    'special_price',
                    'special_from_date',
                    'special_to_date',
                    'weight',
                    'reward_point_product',
                  ]
                : columnList,
          };
      try {
        const response = await axios.post(
          // `${krakendPaths.CATALOG_URL}/admin-api/v1/products/export`,
          `${constants.CATALOG_URL}/v1/catalog-admin/download-product-csv`,
          postdata,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              'Content-Type': 'application/json',
            },
          },
        );
        return response.data;
      } catch (error) {
        throw new Error('Failed to fetch data');
      }
    },
    onError: (err) => {
      addToast('error', `${err}`);
    },
    onSuccess: (data) => {
      addToast('success', 'Successfully Exported');
      setIsExport(false);
    },
  });

  const handleCheckboxExportChange = (identifier: string) => {
    const columnIndex = columnList.findIndex((v) => v === identifier);

    if (columnIndex === -1) {
      setColumnList((prevList) => [...prevList, identifier]);
    } else {
      setColumnList((prevList) =>
        prevList.filter((item) => item !== identifier),
      );
    }
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setColumnList([]);
    } else {
      const allIdentifiers = checkboxes.map((checkbox) => checkbox.identifier);
      setColumnList(allIdentifiers);
    }
    setSelectAll(!selectAll);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsExport(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setIsExport]);

  return (
    <div>
      <DropdownContainer ref={dropdownRef}>
        <DropdownMenu
          size="350px"
          style={{ position: 'absolute', top: '100%', left: 50 }}
        >
          <DropdownItem>
            {checkboxes.map((checkbox) => (
              <CheckboxExport
                key={checkbox.identifier}
                checked={columnList.includes(checkbox.identifier as never)}
                onChange={() => handleCheckboxExportChange(checkbox.identifier)}
                label={checkbox.label}
                identifier={checkbox.identifier}
              />
            ))}
          </DropdownItem>
          <Row>
            <Col style={{ justifyContent: 'end', display: 'flex' }}>
              <Button
                isAction
                style={{ margin: '10px 10px' }}
                onClick={handleSelectAll}
              >
                {selectAll ? 'Unselect All' : 'Select All'}
              </Button>
              <Button
                isPrimary
                style={{ margin: '10px 10px' }}
                onClick={() => mutate()}
              >
                {isLoading ? <Spinner /> : 'Export'}
              </Button>
            </Col>
          </Row>
        </DropdownMenu>
      </DropdownContainer>
    </div>
  );
};

export default ExportDropdown;
