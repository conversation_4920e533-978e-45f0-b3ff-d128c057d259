import { useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  CommonFilterProps,
  IDropdownItem,
  IPagination,
  PaginatedInterface,
} from '../../../types/types';
import TopBar from '../../topbar/TopBar';
import ReturnApprovedTable from '../../table/new-return-modules/ReturnApproved';
import { ReturnApprovedTableColumn } from '../../table/new-return-modules/columns';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import LazyLoading from '../../UI-components/LazyLoading';
import useToast from '../../../hooks/useToast';
import { NetworkStatus, useQuery as useGQLQuery } from '@apollo/client';

import returnClient from '../../../apollo-client/ReturnClient';
import buildQueryParams from '../../../utils/helper/buildQueryParams';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

const ReturnApprovedLayout = () => {
  const [page, setPage] = useState<number>(1);

  const [totalPages, setTotalPages] = useState(0);
  // const [returnData, setReturnData] = useState<any[]>([]);
  // const [paginatedResult, setPaginatedResult] = useState<IPagination>();

  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  const [filters, setFilters] = useState<CommonFilterProps>({
    page: 1,
    rowsPerPage: 10,
  });

  const addToast = useToast();
  const axios = useAxios();
  const {
    data,
    isLoading,
    refetch: refecthSearch,
    isFetching,
    isRefetching,
  } = useQuery({
    queryKey: ['approvedReturnList', filters],
    queryFn: async () => {
      const params = buildQueryParams({
        ...filters,
        page: filters.page,
        size: filters.rowsPerPage,
      });

      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/returns/approved-returns?${params}`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response;
    },
    onSuccess: (data: any) => {
      const total = data?.total || 0;
      setTotalPages(Math.ceil(total / (filters.rowsPerPage as number)));
    },
  });

  useEffect(() => {
    const regex = /^[\d-]+$/;
    if (searchContent != null || undefined || '') {
      if (searchContent && regex.test(searchContent)) {
        refecthSearch();
      } else {
        addToast('info', 'Please provide valid order id');
      }
    } else {
      refecthSearch();
    }
  }, [filters]);
  return (
    <>
      {isLoading || isRefetching ? (
        <>
          <LazyLoading />
        </>
      ) : (
        <>
          <ReturnApprovedTable
            searchContent={searchContent}
            setSearchContent={setSearchContent}
            data={data?.data}
            page={page}
            setPage={setPage}
            totalPage={totalPages}
            refetch={refecthSearch}
            filters={filters}
            setFilters={setFilters}
            count={data?.total}
            // pagination={paginatedResult}
            isFetching={isLoading}
            isRefetching={isLoading}
          />
        </>
      )}
    </>
  );
};

export default ReturnApprovedLayout;
