import React, { <PERSON>actNode, useEffect, useState } from 'react';
import { IReturnDTO } from '../../../../types/new-return-types';
import {
  ColumnDef,
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useParams } from 'react-router-dom';
import { Span } from '@zendeskgarden/react-typography';
import {
  CircleIcon,
  DownIcon,
  LeftArrowIcon,
  RemoveIcon,
  RightArrowIcon,
} from '../../../../utils/icons';
import { baseTheme } from '../../../../themes/theme';
import { Tag } from '../../../UI-components/Tags';
import { useApolloClient } from '@apollo/client';
import { Col, Row as _Row } from '../../../UI-components/Grid';
import { Checkbox, Fieldset, Label } from '@zendeskgarden/react-forms';
import styled from 'styled-components';
import { _TableContainer } from '../../../UI-components/Table';
import { DataTable } from '../../../table/new-return-modules/DataTable';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '../../../UI-components/Button';
import ReturnDTOModal from '../../../modal/new-return-module/return-item-request/ReturnDTO';
import useAxios from '../../../../hooks/useAxios';
import { IconButton } from '../../../UI-components/IconButton';
import { Pagination } from '@zendeskgarden/react-pagination';
import {
  Dropdown,
  Menu,
  Trigger,
  Field as _Field,
} from '@zendeskgarden/react-dropdowns';
import LazyLoading from '../../../UI-components/LazyLoading';
import useToast from '../../../../hooks/useToast';
import { format } from 'date-fns';
import RemoveDTO from '../../../modal/new-return-module/return-item-request/RemoveDTO';
import SearchReturnIdModal from '../../../modal/new-return-module/SearchReturnId';
import constants from '../../../../constants';
import buildQueryParams from '../../../../utils/helper/buildQueryParams';
import krakendPaths from '../../../../constants/krakendPaths';

const DField = styled(_Field)`
  padding: ${baseTheme.space.sm};
`;

const Container = styled.div`
  padding: 25px 27px;
`;

const Row = styled(_Row)`
  margin-bottom: 30px;
`;

const GridRow = styled(_Row)``;

const TableConatiner = styled(_TableContainer)``;

const ReturnDTO = ({
  returnOrder,
  isOrderLoading,
  orderId,
}: {
  returnOrder: any[];
  isOrderLoading: boolean;
  orderId: string;
}) => {
  const [returnDTO, setReturnDTO] = useState<IReturnDTO[]>([]);
  const [count, setCount] = useState(0);
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const apolloClient = useApolloClient();
  const [awbNumber, setAwbNumber] = useState<string>('123456789');

  const [editVisible, setEditVisible] = useState(false);
  const { returnId } = useParams();
  const addToast = useToast();

  const [filters, setFilters] = useState({
    rowsPerPage: 20,
    pageNumber: 1,
    order_id: orderId,
    return_id: returnId,
  });

  // useEffect(() => {
  //   console.log('Order ID', orderId);
  //   console.log('Consdition', orderId === undefined);
  // }, [orderId]);
  const queryClient = useQueryClient();
  const [visible, setVisible] = useState(false);
  const [createModalVisible, setCReateModalVisible] = useState<boolean>(false);

  const [returnItemInfo, setreturnItemInfo] = useState<any>();

  const [returnItemId, setreturnItemId] = useState<number>();

  const axios = useAxios();
  const {
    data,
    isLoading: queryLoading,
    error: queryError,
    refetch,
  } = useQuery({
    queryKey: ['returnDTO', filters],
    queryFn: async () => {
      const params = buildQueryParams({
        ...filters,
        page: filters.pageNumber,
        size: filters.rowsPerPage,
      });
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/admin/dto?${params}`,
        {
          headers: {
            'x-api-key': constants.RETURN_API_KEY,
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    staleTime: 0,
    cacheTime: 0,
    onSuccess: (data: any) => {
      const result: IReturnDTO[] = data.result;
      setCount(data.count);

      const returnDTOs: any[] = result.map((rem: IReturnDTO) => ({
        id: rem.id,
        awb_number: rem.awb_number,
        order_id: rem.order_id,
        return_id: rem.return_id,
        dto_pack_date: rem.dto_open_date,
        product_name: rem.product_name,
        attachments: rem.attachments,
        qty_received: rem.qty,
        status: rem.status,
        inspection_remark: rem.inspection_remark,
        resolution: rem.resolution,
        moved_to_next_stage: rem.next_stage_date,
        dto_stage: rem.stage,
        current_stage: rem.curr_status,
        created_At: rem.created_At,
      }));
      console.log('faisalkhan', returnDTOs, result);
      setReturnDTO(returnDTOs);
    },
    onError: (error: any) => {
      console.error('Error:', error.message);
      setReturnDTO([]);
    },
    retry: 1, // Optional: Retry once on failure
  });

  const disabledColumns = ['Awb Number', 'Order Id', 'Return Id'];
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Awb Number',
    'Order Id',
    'Return Id',
    'DTO Package Open Date',
    'Attachments',
    // 'Updated At',
    'Product Name',
    'Qty Received',
    'Status',
    'Inspection Remark',
    'Resolution',
    'Moved to next Stage on',
    'DTO Stage',
    'Current Status',
    'Action',
  ]);

  const returnDTOColumn: ColumnDef<IReturnDTO>[] = [
    {
      accessorKey: 'id',
      enableHiding: true,
    },
    {
      id: 'new',
      enableColumnFilter: true,
      header: () => {
        return (
          <>
            <Span
              style={{ cursor: 'pointer' }}
              onClick={() => {
                setVisible(true);
              }}
              hue="white"
            >
              New
            </Span>
          </>
        );
      },
      cell: ({ row }) => {
        const axios = useAxios();
        const { mutate: deleteReturnDto, isLoading: deleteReturnDTOLoading } =
          useMutation(
            async (body: any) => {
              const response = await axios.delete(
                `${krakendPaths.RETURN_URL}/admin-api/v1/admin/dto/${body.id}`,
                {
                  headers: {
                    Authorization: `Bearer ${localStorage.getItem(
                      'api-token',
                    )}`,
                    'x-api-key': constants.RETURN_API_KEY,
                  },
                },
              );
              return response;
            },
            {
              onError: (error: any) => {
                console.log(error);
                addToast('error', error.message);
              },
              onSuccess: (data) => {
                addToast('success', 'Deleted Successfully');
                refetch();
                queryClient.invalidateQueries(['returnDTO']);
              },
            },
          );

        const client = useApolloClient();

        const dtoID: number = row.original.id;

        // console.log('Row Orginal', dtoID);

        // const Data = client.readQuery({ query: GET_RETURN_DTO, id: dtoID });
        // console.log(Data);
        const [visibleRemove, setVisibleRemove] = useState<boolean>(false);

        const handleDelete = (dtoID: number) => {
          deleteReturnDto({ id: dtoID });
          refetch();
        };
        return (
          <>
            <Row>
              {/* <IconButton
                size="medium"
                // onClick={() => setEditVisible(!visible)}
              >
                <EditIconDTO />
              </IconButton> */}
              {row.original?.status !== 'close' && (
                <IconButton
                  onClick={() => {
                    setVisibleRemove(true);
                  }}
                  size="medium"
                >
                  <RemoveIcon style={{ color: baseTheme.colors.dangerHue }} />
                </IconButton>
              )}
            </Row>

            {visibleRemove && (
              <RemoveDTO
                setVisible={setVisibleRemove}
                remove={() => handleDelete(dtoID)}
                isLoading={deleteReturnDTOLoading}
              />
            )}
          </>
        );
      },
      enableSorting: false,
      enableHiding: false,
      enablePinning: true,
    },
    {
      accessorKey: 'order_id',
      header: 'Order Id',
      cell: ({ row }) => {
        const order_id: string = row.getValue('order_id');
        return <>{order_id}</>;
      },
      enablePinning: true,
    },
    {
      accessorKey: 'return_id',
      header: 'Return Id',
      cell: ({ row }) => {
        const salesReturnId = parseInt(row.getValue('return_id'));
        return <>{salesReturnId}</>;
      },
      enablePinning: true,
    },
    {
      accessorKey: 'awb_number',
      header: 'Awb Number',
      cell: ({ row }) => {
        const awbNumber = row.getValue('awb_number');
        return <>{awbNumber ?? <Tag>Not Available</Tag>} </>;
      },
    },
    {
      accessorKey: 'dto_open_date',
      header: 'DTO Package Open Date',
      cell: ({ row }) => {
        const rawDate = row.getValue('dto_open_date');

        if (!rawDate) return <Tag>Not Available</Tag>; // Handle empty/null values safely

        const dtoPack = new Date(rawDate as string);

        if (isNaN(dtoPack.getTime())) return <>Invalid Date</>; // Handle invalid dates

        return <>{format(dtoPack, 'MM/dd/yyyy HH:mm:ss')}</>;
      },
    },

    {
      accessorKey: 'attachments',
      header: 'Attachments',
      cell: ({ row }) => {
        return <>{row.original?.attachments?.length}</>;
      },
    },
    {
      accessorKey: 'product_name',
      header: 'Product Name',
      cell: ({ row }) => {
        const productName: string = row.getValue('product_name');
        return <>{productName}</>;
      },
    },
    {
      accessorKey: 'qty_receivedF',
      header: 'Qty Received',
      cell: ({ row }) => {
        const qty = parseInt(row.getValue('qty_received'));
        return <>{qty}</>;
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status: string = row.getValue('status');
        return (
          <>
            {status.toLowerCase() === 'open' ? (
              <>
                <Tag isOpen isPill hue={baseTheme.colors.transparentGreen}>
                  <Tag.Avatar>
                    <CircleIcon
                      color={baseTheme.colors.solidGreen}
                      style={{
                        width: baseTheme.iconSizes.xs,
                        height: baseTheme.iconSizes.xs,
                      }}
                    />
                  </Tag.Avatar>
                  <Span>{status}</Span>
                </Tag>
              </>
            ) : (
              <>
                <Tag isPill hue={baseTheme.colors.transparentRed}>
                  <Tag.Avatar>
                    <CircleIcon
                      color={baseTheme.colors.solidRed}
                      style={{
                        width: baseTheme.iconSizes.xs,
                        height: baseTheme.iconSizes.xs,
                      }}
                    />
                  </Tag.Avatar>
                  <Span>{status}</Span>
                </Tag>
              </>
            )}
          </>
        );
      },
    },
    {
      accessorKey: 'inspection_remark',
      header: 'Inspection Remark',
      cell: ({ row }) => {
        const inspectionRemark: string = row.getValue('inspection_remark');
        return <>{inspectionRemark}</>;
      },
    },
    {
      accessorKey: 'resolution',
      header: 'Resolution',
      cell: ({ row }) => {
        const resolution = row.getValue('resolution');

        return <>{resolution}</>;
      },
    },
    {
      accessorKey: 'next_stage_date',
      header: 'Moved to next Stage on',
      cell: ({ row }) => {
        const moveStage = Date.parse(row.getValue('next_stage_date'));
        return <>{moveStage ? format(moveStage, 'MM-dd-yyyy HH:mm:ss') : ''}</>;
      },
    },
    {
      accessorKey: 'stage',
      header: 'DTO Stage',
      cell: ({ row }) => {
        const dtoStage: string = row.getValue('stage');
        return <>{dtoStage}</>;
      },
    },
    {
      accessorKey: 'curr_status',
      header: 'Current Status',
      cell: ({ row }) => {
        const currentStage: string = row.getValue('curr_status');
        return (
          <>
            <>{currentStage}</>
          </>
        );
      },
    },
    {
      accessorKey: 'created_At',
      header: 'Created At',
      cell: ({ row }) => {
        // console.log('Row', row.original);
        return (
          <>
            {row.original.created_At || row.original.created_At != null
              ? format(new Date(row.original.created_At), 'MM/dd/yyyy HH:mm:ss')
              : ''}
          </>
        );
      },
    },
  ];

  const options = {
    returnDTO,
    columns: returnDTOColumn,
    data: returnDTO,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  };

  const table = useReactTable(options);

  useEffect(() => {
    table.setPageSize(Number(filters.rowsPerPage));
  }, []);

  const [rotated, setRotated] = useState<boolean | undefined>();
  const [isChecked, setIsChecked] = useState(false);

  // const handleCheckboxChange = (event: any, column: any) => {
  //   const { checked } = event.target;
  //   setIsChecked(checked);
  //   // console.log(column);
  //   column.toggleVisibility(checked);
  // };

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    setIsChecked(checked);
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);

      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );

      column.toggleVisibility(false);
    }
  };

  return (
    <>
      {isOrderLoading ? (
        <>
          <LazyLoading />
        </>
      ) : (
        <>
          <Container>
            <Row justifyContent="end">
              <Dropdown
                onSelect={(item) => alert(`You selected a ${item}`)}
                onStateChange={(options) =>
                  Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
                }
              >
                <Trigger>
                  <Button isPrimary>
                    Select Item
                    <Button.EndIcon isRotated={rotated}>
                      <DownIcon
                        style={{
                          height: baseTheme.iconSizes.md,
                          width: baseTheme.iconSizes.md,
                        }}
                      />
                    </Button.EndIcon>
                  </Button>
                </Trigger>
                <Menu
                  style={{
                    width: baseTheme.components.dimension.width.base200,
                    transform: 'translateX(4px)',
                    borderRadius: baseTheme.borderRadii.lg,
                  }}
                >
                  <Fieldset>
                    {table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <>
                            {!column.columnDef.enableHiding &&
                              !column.columnDef.enableColumnFilter && (
                                <DField>
                                  <Checkbox
                                    disabled={disabledColumns?.includes(
                                      column.columnDef.header as string,
                                    )}
                                    key={column.id}
                                    checked={alreadyEnabledColumn?.includes(
                                      column.columnDef.header as string,
                                    )}
                                    onChange={(e) =>
                                      handleCheckboxChange(
                                        e,
                                        column,
                                        column.columnDef.header as string,
                                      )
                                    }
                                  >
                                    <Label>
                                      <>
                                        {column.columnDef.header as ReactNode}
                                      </>
                                    </Label>
                                  </Checkbox>
                                </DField>
                              )}
                          </>
                        );
                      })}
                  </Fieldset>
                </Menu>
              </Dropdown>
            </Row>
            <Row justifyContent="center">
              <TableConatiner>
                <DataTable
                  data={returnDTO}
                  columns={returnDTOColumn}
                  table={table}
                  alreadyEnabledColumn={alreadyEnabledColumn}
                />
              </TableConatiner>
            </Row>
            {table.getRowModel().rows?.length != 0 && (
              <GridRow justifyContent="end">
                <Col size={2}>
                  <Row justifyContent="start" alignItems="center">
                    <Button
                      style={{
                        maxWidth: baseTheme.components.dimension.width.base100,
                      }}
                      size="medium"
                      isAction
                      onClick={() => {
                        setFilters((prev) => ({
                          ...prev,
                          page: prev.pageNumber - 1,
                        }));
                      }}
                      disabled={filters.pageNumber <= 1 ? true : false}
                    >
                      <Button.StartIcon>
                        <LeftArrowIcon />
                      </Button.StartIcon>
                      Previous
                    </Button>
                  </Row>
                </Col>
                <Col size={6}>
                  <GridRow justifyContent="center" alignItems="center">
                    <Pagination
                      color={baseTheme.colors.deepBlue}
                      totalPages={Math.ceil(
                        count / table.getState().pagination.pageSize,
                      )}
                      pagePadding={2}
                      currentPage={filters.pageNumber}
                      onChange={(e) =>
                        setFilters((prev) => ({ ...prev, page: e }))
                      }
                    />
                  </GridRow>
                </Col>
                <Col size={2}>
                  <GridRow justifyContent="start" alignItems="end">
                    <Button size="medium" isAction>
                      {(filters.pageNumber - 1) *
                        table.getState().pagination.pageSize +
                        1}
                      -
                      {count <
                      filters.pageNumber * table.getState().pagination.pageSize
                        ? count
                        : filters.pageNumber *
                          table.getState().pagination.pageSize}{' '}
                      of {count}
                    </Button>
                  </GridRow>
                </Col>
                <Col size={2}>
                  <GridRow justifyContent="end" alignItems="end">
                    <Button
                      style={{
                        maxWidth: baseTheme.components.dimension.width.base100,
                      }}
                      size="medium"
                      isAction
                      onClick={() =>
                        setFilters((prev) => ({
                          ...prev,
                          page: prev.pageNumber + 1,
                        }))
                      }
                      disabled={
                        filters.pageNumber >
                        Math.ceil(
                          count / table.getState().pagination.pageSize,
                        ) -
                          1
                          ? true
                          : false
                      }
                    >
                      Next
                      <Button.EndIcon>
                        <RightArrowIcon />
                      </Button.EndIcon>
                    </Button>
                  </GridRow>
                </Col>
              </GridRow>
            )}
            {visible && (
              <>
                <SearchReturnIdModal
                  setCreateModalVisible={setCReateModalVisible}
                  setVisible={setVisible}
                  setreturnItemInfo={setreturnItemInfo}
                  returnItemId={returnItemId}
                  setreturnItemId={setreturnItemId}
                />
              </>
            )}

            {createModalVisible &&
              returnItemInfo != undefined &&
              returnItemId != undefined && (
                <>
                  <ReturnDTOModal
                    setVisible={setCReateModalVisible}
                    returnItemId={returnItemId}
                    returnItemInfo={returnItemInfo}
                  />
                </>
              )}
          </Container>
        </>
      )}
    </>
  );
};

export default ReturnDTO;
