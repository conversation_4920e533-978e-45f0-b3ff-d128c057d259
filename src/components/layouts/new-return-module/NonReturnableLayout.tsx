import React, { useEffect, useState } from 'react';
import { NonReturnableTableColumn } from '../../table/new-return-modules/columns';
import NonReturnableTable from '../../table/new-return-modules/NonReturnable';
import useAxios from '../../../hooks/useAxios';
import { INonReturnAble } from '../../../types/new-return-types';
import LazyLoading from '../../UI-components/LazyLoading';
import returnClient from '../../../apollo-client/ReturnClient';
import { useQuery } from '@tanstack/react-query';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

export interface CommonGQFilter {
  rowsPerPage: number;
  pageNumber: number;
  order_id?: string;
  return_id?: string;
}

const NonReturnableLayout = () => {
  const [page, setPage] = useState<number>(1);
  const [count, setCount] = useState(20);

  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );

  const [nonReturnableData, setNonReturnableData] = useState<
    NonReturnableTableColumn[]
  >([]);

  const [filters, setFilters] = useState<any>({
    rowsPerPage: 20,
    pageNumber: 1,
  });

  const axios = useAxios();
  const {
    data,
    error,
    isLoading: queryLoading,
    isRefetching,
    refetch,
  } = useQuery({
    queryKey: ['getNonReturnable', filters],
    queryFn: async () => {
      const response = await axios.get(
        `${krakendPaths.RETURN_URL}/admin-api/v1/config/non-returnable`,
        {
          params: {
            page: filters.pageNumber,
            size: filters.rowsPerPage,
            sku: filters.sku,
            product_name: filters.product_name,
          },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': constants.RETURN_API_KEY,
          },
        },
      );
      return response;
    },
    onSuccess: (data: any) => {
      const result: INonReturnAble[] = data.data;
      setCount(result.length);
      const nonReturnAble: NonReturnableTableColumn[] = result.map(
        (rem: INonReturnAble, index: number) => {
          return {
            sku: rem.sku,
            productName: rem.product_name,
            createdAt: rem.created_at,
            updatedAt: rem.updated_at,
          };
        },
      );
      setNonReturnableData(nonReturnAble);
    },
    onError: (error: any) => {
      console.error(error);
    },
  });

  // useEffect(() => {
  //   refetch();
  // }, [filters]);
  return (
    <>
      {queryLoading || isRefetching ? (
        <LazyLoading />
      ) : (
        <>
          <NonReturnableTable
            searchContent={searchContent}
            setSearchContent={setSearchContent}
            queryData={nonReturnableData}
            page={page}
            setPage={setPage}
            totalPage={20}
            filters={filters}
            setFilters={setFilters}
            count={count}
          />
        </>
      )}
    </>
  );
};

export default NonReturnableLayout;
