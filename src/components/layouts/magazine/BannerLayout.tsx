import React, { useEffect, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { baseTheme } from '../../../themes/theme';
import { Button } from '../../UI-components/Button';
import AddBannerModal from '../../modal/magazine/AddBannerModal';
import BannerTable from '../../table/magazine/BannerTable';
// import { useQuery } from '@apollo/client';
import useToast from '../../../hooks/useToast';
import { GET_BANNER, UPDATE_BANNER, DELETE_BANNER } from '../../../graphql/';
import { useAuth } from '../../providers/AuthProvider';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import krakendPaths from '../../../constants/krakendPaths';

const BannerLayout = () => {
  const addToast = useToast();
  const { logout } = useAuth();
  const axios = useAxios();
  const [addBannerModal, setAddBannerModal] = useState(false);
  const [newBannerAdded, setNewBannerAdded] = useState(false);
  // const [searchContent, setSearchContent] = useState<string | undefined>(
  //     undefined,
  // );
  const [filters, setFilters] = useState<any>({
    rowsPerPage: 20,
    pageNumber: 1,
  });

  // const {
  //   error: queryError,
  //   loading: queryLoading,
  //   data: queryData,
  //   refetch,
  // } = useQuery(GET_BANNER, {
  //   fetchPolicy: 'network-only',
  //   variables: {
  //     ...filters,
  //   },
  //   onError: ({
  //     graphQLErrors,
  //     networkError,
  //   }: {
  //     graphQLErrors: any;
  //     networkError: any;
  //   }) => {
  //     if (networkError) {
  //       addToast('error', networkError);
  //     }
  //     if (graphQLErrors) {
  //       addToast('error', graphQLErrors[0].message);
  //       if (graphQLErrors[0].message === 'Unauthorized') {
  //         addToast('error', 'Session Time Out');
  //         logout();
  //       }
  //     }
  //   },
  // });

  const {
    data: queryData,
    refetch,
    isLoading: publisherLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['get-publisher', filters],
    queryFn: async (): Promise<any> => {
      const response = await axios.get(
        // 'http://localhost:3002/magazines',
        `${krakendPaths.MAGAZINE_URL}/admin-api/v1/publishers`,
        {
          params: {
            page: filters.pageNumber,
            pageSize: filters?.rowsPerPage,
          },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response;
    },
    // onError: (err) => {
    //   addToast('error', 'Error encountered !!');
    // },
    // onSuccess: (data) => {
    //   setLatestVideo(data.data.rows[0]);
    // },
  });

  useEffect(() => {
    refetch();
  }, [newBannerAdded]);

  return (
    <>
      <Row justifyContent="start">
        <Button
          onClick={() => {
            setAddBannerModal(true);
          }}
          style={{ margin: baseTheme.space.md }}
          isPrimary
        >
          Add Banner
        </Button>
      </Row>
      {addBannerModal && (
        <AddBannerModal
          visible={addBannerModal}
          setVisible={setAddBannerModal}
          setNewBannerAdded={setNewBannerAdded}
        />
      )}
      {queryData && queryData.getBanner && queryData.getBanner.length > 0 && (
        <BannerTable
          data={queryData.getBanner}
          count={queryData.getBanner.length}
          // searchContent={searchContent}
          // setSearchContent={setSearchContent}
          refetch={refetch}
          filters={filters}
          setFilters={setFilters}
        />
      )}
    </>
  );
};

export default BannerLayout;
