import { useState } from 'react';
import { RefundPaymentsColumn } from '../../table/refund/Column';
import RefundPaymentsTable from '../../table/refund/RefundPaymentsTable';

const RefundPaymentsLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: RefundPaymentsColumn[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed

    return (
        <>
            <RefundPaymentsTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default RefundPaymentsLayout;
