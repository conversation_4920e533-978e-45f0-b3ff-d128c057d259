import { useState } from 'react';
import { RazorPayRefundColumn } from '../../table/refund/Column';
import RazorPayRefundsTable from '../../table/refund/RazorPayRefundTable';

const RazorPayRefundsLayout = ({
    rows,
    count,
    refetch,
    filters,
    setFilters,
}: {
    rows: RazorPayRefundColumn[];
    count: number;
    refetch: any;
    filters: any;
    setFilters: React.Dispatch<React.SetStateAction<any>>;
}) => {

    const [searchContent, setSearchContent] = useState<string | undefined>(
        undefined,
    );
    // Add more objects as needed
    console.log('layout data: ', rows);

    return (
        <>
            <RazorPayRefundsTable
                data={rows}
                count={count}
                searchContent={searchContent}
                setSearchContent={setSearchContent}
                refetch={refetch}
                filters={filters}
                setFilters={setFilters}
            />
        </>
    );
};

export default RazorPayRefundsLayout;
