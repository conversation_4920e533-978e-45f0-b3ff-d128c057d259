import { useState } from 'react';
import { ReimbursementColumn, ReimbursementNewColumn } from '../../../gql/graphql';
import { ReimbursementRow } from '../../../gql/graphql';
import ReimbursementTable from '../../table/reimbursement/ReimbursementTable';
import { ReimbursementFiltersProps } from '../../../types/types';

const ReimbursementLayout = ({
  columns,
  rows,
  count,
  refetch,
  filters,
  setFilters,
}: {
  columns: ReimbursementNewColumn[];
  rows: ReimbursementRow[];
  count: number;
  refetch: any;
  filters: ReimbursementFiltersProps;
  setFilters: React.Dispatch<React.SetStateAction<ReimbursementFiltersProps>>;
}) => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  // Add more objects as needed
  return (
    <>
      <ReimbursementTable
        data={rows}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        columnsData={columns}
        count={count}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};

export default ReimbursementLayout;
