import { useState } from 'react';
import { ReimbursementColumn } from '../../../gql/graphql';
import { ReimbursementRow } from '../../../gql/graphql';
import DeletedReimbursementTable from '../../table/reimbursement/DeletedReimbursementTable';
import { ReimbursementFiltersProps } from '../../../types/types';

const DeletedReimbursementLayout = ({
  columns,
  rows,
  count,
  refetch,
  filters,
  setFilters,
}: {
  columns: ReimbursementColumn[];
  rows: ReimbursementRow[];
  count: number;
  refetch: any;
  filters: ReimbursementFiltersProps;
  setFilters: React.Dispatch<React.SetStateAction<ReimbursementFiltersProps>>;
}) => {
  const [searchContent, setSearchContent] = useState<string | undefined>(
    undefined,
  );
  // Add more objects as needed

  return (
    <>
      <DeletedReimbursementTable
        data={rows}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        columnsData={columns}
        count={count}
        refetch={refetch}
        filters={filters}
        setFilters={setFilters}
      />
    </>
  );
};

export default DeletedReimbursementLayout;
