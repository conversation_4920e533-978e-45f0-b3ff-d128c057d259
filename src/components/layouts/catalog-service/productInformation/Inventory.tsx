import { Label as _Label, Input } from '@zendeskgarden/react-forms';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import React, { useEffect, useState } from 'react';
import { useProductContext } from '../../../../pages/catalog-service/ProductFilterContext';
import { baseTheme } from '../../../../themes/theme';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import AdvanceInventory from '../../../modal/catalog-service/AdvanceInventory';
import styled from 'styled-components';

const Row = styled(_Row)`
  margin: 20px 0px;
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const Inventory = ({ id }: { id?: any }) => {
  const {
    contextProdData,
    contextUpdateProdData,
    setContextProdData,
    setContextUpdateProdData,
  } = useProductContext();

  const [stockStatus, setStockStatus] = useState<string | null>('In Stock');
  const [qty, setQty] = useState<any>(contextProdData?.inventory_details?.qty);

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData((prevState) => ({
      ...prevState,
      inventory_details: {
        ...prevState.inventory_details,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      inventory_details: {
        ...prevState.inventory_details,
        [attribute]: value,
      },
    }));
  };
  // Stock Status
  const [inputStockStatus, setInputStockStatus] = useState('');
  const handleSelectStockStatus = (item: any) => {
    // if (item === 'No Select') {
    //   setStockStatus(null);
    //   updateContextData('is_in_stock', null)
    // } else {
    setStockStatus(item);
    updateContextData('is_in_stock', item === 'In Stock' ? true : false);
    // }
  };
  const handleInputStockStatusChange = (value: any) => {
    setInputStockStatus(value);
  };

  // Quantity
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const qty = value ? parseInt(value, 10) : null;
    const parsedValue = parseFloat(value);
    if (parsedValue <= 0) {
    } else {
      updateContextData('qty', qty);
      setQty(parsedValue);
    }
  };
  const [backorder, setBackOrder] = useState<string | undefined>(
    'No Backorders',
  );
  const [inputBackOrders, setInputBackOrders] = useState('');
  const handleSelectBackOrders = (item: any) => {
    if (item === 'No Backorders') {
      setBackOrder(item);
      updateContextData('backorders', false);
    } else if (item === 'Allow Qty Below 0') {
      setBackOrder(item);
      updateContextData('backorders', true);
    }
  };
  const handleInputBackOrdersChange = (value: any) => {
    setInputBackOrders(value);
  };
  const [minSaleQty, setMinSaleQty] = useState<number | null>(null);
  const [maxSaleQty, setMaxSaleQty] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleMinSaleQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value !== '' ? parseInt(e.target.value, 10) : null;
    setMinSaleQty(value);
    updateContextData('min_sale_qty', value);
  };

  const handleMaxSaleQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value !== '' ? parseInt(e.target.value, 10) : null;
    setMaxSaleQty(value);
    updateContextData('max_sale_qty', value);
  };

  useEffect(() => {
    if (
      minSaleQty !== null &&
      minSaleQty > 0 &&
      maxSaleQty !== null &&
      maxSaleQty > 0 &&
      maxSaleQty < minSaleQty
    ) {
      setError('Maximum quantity should be greater than minimum quantity');
    } else {
      setError(null);
    }
  }, [minSaleQty, maxSaleQty]);

  useEffect(() => {
    if (contextUpdateProdData?.inventory_details?.is_in_stock) {
      setStockStatus(
        contextUpdateProdData?.inventory_details?.is_in_stock === true
          ? 'In Stock'
          : 'Out of Stock',
      );
    } else if (contextProdData?.inventory_details?.is_in_stock != null) {
      setStockStatus(
        contextProdData.inventory_details.is_in_stock === true
          ? 'In Stock'
          : 'Out of Stock',
      );
    }
    if (contextUpdateProdData?.inventory_details?.qty) {
      setQty(contextUpdateProdData?.inventory_details?.qty ?? '');
    } else if (contextProdData?.inventory_details?.qty) {
      setQty(contextProdData.inventory_details.qty);
    }
    if (contextProdData?.inventory_details?.min_sale_qty != undefined) {
      setMinSaleQty(contextProdData.inventory_details.min_sale_qty);
    }
    if (contextProdData?.inventory_details?.max_sale_qty != undefined) {
      setMaxSaleQty(contextProdData.inventory_details.max_sale_qty);
    }
  }, [contextProdData]);

  return (
    <div>
        <Row>
          <div style={{width: '300px'}}>
          <ProductDropdown
            label="Stock Status"
            options={['In Stock', 'Out of Stock']}
            selectedItem={stockStatus}
            inputValue={inputStockStatus}
            onSelect={handleSelectStockStatus}
            onInputValueChange={handleInputStockStatusChange}
          />
          </div>
        </Row>
        <Row style={{width: '300px'}}>
          <Label>Quantity</Label>
          <Input
            type="number"
            value={qty}
            onChange={handleQuantityChange}
            disabled={
              id
                ? contextProdData.type_id === 'grouped'
                : contextUpdateProdData.type_id === 'grouped'
            }
          />
        </Row>
      {contextProdData.type_id != 'grouped' && (
        <>
              <Label>Minimum Qty Allowed in Shopping Cart (Global)</Label>
            <Row style={{width: '300px'}}>
              <Input 
                type="number"
                value={minSaleQty ?? ''}
                onChange={handleMinSaleQtyChange}
              />
            </Row>
            <Label>Maximum Qty Allowed in Shopping Cart (Global)</Label>
            <Row style={{width: '300px'}}>
              <Input
                type="number"
                value={maxSaleQty ?? ''}
                onChange={handleMaxSaleQtyChange}
              />
              {error && <div style={{ color: 'red' }}>{error}</div>}
            </Row>
            <Row>
              <div style={{width: '300px'}}> 
              <ProductDropdown
                label='Backorders'
                options={['Allow Qty Below 0', 'No Backorders']}
                selectedItem={backorder}
                inputValue={inputBackOrders}
                onSelect={handleSelectBackOrders}
                onInputValueChange={handleInputBackOrdersChange}
              />
              </div>
            </Row>
        </>
      )}
    </div>
  );
};

export default Inventory;
