import {
  Field,
  Label as _Label,
  Input as _Input,
  Toggle as _Toggle,
  MediaInput as _MediaInput,
  Textarea,
} from '@zendeskgarden/react-forms';
import isEqual from 'lodash/isEqual';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import React, { useEffect, useRef, useState } from 'react';
import {
  CategoryData,
  DropOption,
  useProductContext,
} from '../../../../pages/catalog-service/ProductFilterContext';
import { baseTheme } from '../../../../themes/theme';
import { useParams } from 'react-router-dom';
import useToast from '../../../../hooks/useToast';
import ProductDropdown from '../../../dropdown/catalog-service/ProductDropdown';
import { Datepicker } from '@zendeskgarden/react-datepickers';
import { CalenderIcon, CrossIcon } from '../../../../utils/icons';
import AddNewCategory from '../../../modal/catalog-service/AddNewCategory';
import styled from 'styled-components';
import {
  Dropdown,
  Multiselect,
  Field as DropField,
  Menu,
  Item,
  Label as DropLabel,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import { Tag } from '@zendeskgarden/react-tags';
import { debounce } from 'lodash';
import {
  QueryFunctionContext,
  useInfiniteQuery,
  useQuery,
} from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import { Spinner } from '@zendeskgarden/react-loaders';
import routes from '../../../../constants/routes';
import constants from '../../../../constants';
import CategoryAssociated from '../CategoryAssociated';
import krakendPaths from '../../../../constants/krakendPaths';

const Main = styled.div`
  height: 1000px;
`;

const Row = styled(_Row)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)};
  margin: 20px 0px;
`;

const Toggle = styled(_Toggle)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)};
`;

const MediaInput = styled(_MediaInput)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const Input = styled(_Input)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)};
`;

const Label = styled(_Label)`
  color: ${baseTheme.colors.deepBlue};
`;

const capitalizeFirstLetter = (item: string) => {
  return item.charAt(0).toUpperCase() + item.slice(1);
};
const handleKeyPress = (e: any) => {
  const allowedChars = /^[a-zA-Z0-9 ()\-.,\/]*$/;
  if (!allowedChars.test(e.key)) {
    e.preventDefault();
  }
};

const General = ({ id }: { id?: any }) => {
  const {
    contextProdData,
    contextUpdateProdData,
    setContextProdData,
    setContextUpdateProdData,
    manufacturerData,
    manfacturerList,
    countryList,
    visibilityData,
    visibilityList,
    countryData,
    categoryIds,
    setCategoryIds,
  } = useProductContext();

  const [status, setStatus] = useState<boolean | undefined>(
    contextProdData?.status,
  );
  const [type, setType] = useState<string | undefined>(() => {
    const typeId = contextProdData?.type_id;

    switch (typeId) {
      case 'simple':
        return 'Simple Product';
      case 'grouped':
        return 'Grouped Product';
      case 'virtual':
        return 'Virtual Product';
      default:
        return undefined;
    }
  });
  const [inputProdType, setInputProdType] = useState('');
  const [visibility, setVisibility] = useState<string | undefined>();
  const [inputVisibility, setInputVisibility] = useState();
  const [categName, setCategName] = useState('');
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [manufactur, setManufactur] = useState<string | undefined>();
  const [inputManufacturer, setInputManufacturer] = useState('');
  const [country, setCountry] = useState<string | undefined>();
  const [inputCountry, setInputCountry] = useState('');

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      attributes_list: {
        ...prevState.attributes_list,
        [attribute]: value,
      },
    }));
  };

  // status

  const handleChangeStatus = () => {
    setContextProdData((prevState) => ({
      ...prevState,
      status: !status,
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      status: !status,
    }));
    setStatus(!status);
  };

  // product type
  const handleSelectProdType = (item: string) => {
    setType(item);
    let productType: string;

    switch (item) {
      case 'Simple Product':
        productType = 'simple';
        break;
      case 'Grouped Product':
        productType = 'grouped';
        break;
      case 'Virtual Product':
        productType = 'virtual';
        break;
      default:
        productType = item;
    }
    setContextProdData((prevState) => ({
      ...prevState,
      type_id: productType,
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      type_id: productType,
    }));
  };
  const handleInputProdTypeChange = (value: string) => {
    setInputProdType(value);
  };

  // visibility
  const handleSelectVisibility = (item: any) => {
    if (item === 'Loading...') {
      setVisibility(undefined);
      updateContextData('visibility', null);
    }
    setVisibility(item);
    const selectedVisibility = visibilityData?.find(
      (val) => val.value === item,
    );
    const visibleId = selectedVisibility?.id;
    if (visibleId) {
      setContextProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          visibility: item,
        },
      }));
      setContextUpdateProdData((prevState) => ({
        ...prevState,
        attributes_list: {
          ...prevState.attributes_list,
          visibility: visibleId,
        },
      }));
    }
  };
  const handleInputVisibilityChange = (value: any) => {
    setInputVisibility(value);
  };

  // Category

  const axios = useAxios();
  const addToast = useToast();

  const fetchCategoryIds = async (prodIds: number[]) => {
    try {
      const response: any = await axios.post(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/search-categories`,
        // `${constants.CATALOG_URL}/v1/catalog-admin/category/search-categories`,
        {
          category_ids: prodIds,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      return response?.search_categories;
      // return response;
    } catch (error) {
      console.log('Error: ', `${error}`);
    }
  };

  const [isOpenCategory, setIsOpenCategory] = useState(false);

  useEffect(() => {
    const selectedCategories = selectedItems.map((item) => {
      const parts = item.split(' (ID: ');
      const name = parts[0];
      const id = parts.length > 1 ? parseInt(parts[1].replace(')', '')) : null;
      return { name, id };
    });

    const categoryIds = selectedCategories
      .map((item) => item.id)
      .filter((id): id is number => id !== null);

    if (categoryIds.length > 0) {
      if (!isEqual(contextProdData.category_associated, categoryIds)) {
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          category_associated: [...categoryIds],
        }));
      }
      setCategoryIds(categoryIds);
    }
  }, [selectedItems]);

  // Manufacturer

  const handleSelectManufacturer = (item: any) => {
    if (item === 'Loading...') {
      setManufactur(undefined);
      updateContextData('manufacturer', null);
    } else {
      setManufactur(item);
      const selectedManufacturer = manufacturerData?.find(
        (val) => val.value === item,
      );
      const manufactId = selectedManufacturer?.id;
      if (manufactId) {
        setContextProdData((prevState) => ({
          ...prevState,
          attributes_list: {
            ...prevState.attributes_list,
            manufacturer: item,
          },
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          attributes_list: {
            ...prevState.attributes_list,
            manufacturer: manufactId,
          },
        }));
      }
    }
  };
  const handleInputManufacturerChange = (value: any) => {
    setInputManufacturer(value);
  };

  // Country of Manufacturer

  const handleSelectCountry = (item: string) => {
    if (item === '' || item === 'Loading...') {
      setCountry(undefined);
      updateContextData('country_of_manufacture', null);
    } else {
      setCountry(item);
      const selectedCountry = countryData?.find((val) => val.value === item);
      const countryId = selectedCountry?.id;
      if (countryId) {
        setContextProdData((prevState) => ({
          ...prevState,
          attributes_list: {
            ...prevState.attributes_list,
            country_of_manufacture: item,
          },
        }));
        setContextUpdateProdData((prevState) => ({
          ...prevState,
          attributes_list: {
            ...prevState.attributes_list,
            country_of_manufacture: countryId,
          },
        }));
      }
    }
  };
  const handleInputCountryChange = (value: string) => {
    setInputCountry(value);
  };

  useEffect(() => {
    if (contextUpdateProdData?.attributes_list?.country_of_manufacture) {
      const selectedCountry = countryData?.find(
        (val) =>
          val.id ===
          contextUpdateProdData?.attributes_list?.country_of_manufacture,
      );
      const countryValue = selectedCountry?.value;
      setCountry(countryValue);
    } else if (contextProdData?.attributes_list?.country_of_manufacture) {
      setCountry(contextProdData?.attributes_list?.country_of_manufacture);
    }

    if (contextUpdateProdData?.status) {
      setStatus(contextUpdateProdData.status);
    } else if (contextProdData.status != undefined) {
      setStatus(contextProdData.status);
    }

    if (contextUpdateProdData?.attributes_list?.visibility) {
      const selectedVisibility = visibilityData?.find(
        (val) => val.id === contextUpdateProdData?.attributes_list?.visibility,
      );
      const visibleValue = selectedVisibility?.value;
      setVisibility(visibleValue);
    } else if (contextProdData?.attributes_list?.visibility) {
      setVisibility(contextProdData?.attributes_list?.visibility);
    }

    if (contextUpdateProdData?.attributes_list?.manufacturer) {
      const selectedManufacturer = manufacturerData?.find(
        (val) =>
          val.id === contextUpdateProdData?.attributes_list?.manufacturer,
      );
      const manufactValue = selectedManufacturer?.value;
      setManufactur(manufactValue);
    } else if (contextProdData?.attributes_list?.manufacturer) {
      setManufactur(contextProdData?.attributes_list?.manufacturer);
    }

    if (contextUpdateProdData?.type_id) {
      let productType;

      switch (contextUpdateProdData.type_id) {
        case 'simple':
          productType = 'Simple Product';
          break;
        case 'grouped':
          productType = 'Grouped Product';
          break;
        case 'virtual':
          productType = 'Virtual Product';
          break;
        default:
          productType = contextUpdateProdData.type_id;
      }

      setType(productType);
    } else if (contextProdData?.type_id) {
      let productType;

      switch (contextProdData.type_id) {
        case 'simple':
          productType = 'Simple Product';
          break;
        case 'grouped':
          productType = 'Grouped Product';
          break;
        case 'virtual':
          productType = 'Virtual Product';
          break;
        default:
          productType = contextProdData.type_id;
      }

      setType(productType);
    }

    if (contextUpdateProdData?.attributes_list?.pd_expiry_date) {
      setProdExpiry(
        new Date(contextUpdateProdData.attributes_list.pd_expiry_date),
      );
    } else if (contextProdData?.attributes_list?.pd_expiry_date) {
      setProdExpiry(new Date(contextProdData.attributes_list.pd_expiry_date));
    }

    if (contextUpdateProdData?.attributes_list?.news_from_date) {
      setProdAsNewFrom(
        new Date(contextUpdateProdData.attributes_list.news_from_date),
      );
    } else if (contextProdData?.attributes_list?.news_from_date) {
      setProdAsNewFrom(
        new Date(contextProdData.attributes_list.news_from_date),
      );
    }

    if (contextUpdateProdData?.attributes_list?.news_to_date) {
      setProdAsNewTo(
        new Date(contextUpdateProdData.attributes_list.news_to_date),
      );
    } else if (contextProdData?.attributes_list?.news_to_date) {
      setProdAsNewTo(new Date(contextProdData.attributes_list.news_to_date));
    }
    if (contextUpdateProdData?.attributes_list?.warranty) {
      setWarranty(contextUpdateProdData.attributes_list.warranty);
    } else if (contextProdData?.attributes_list?.warranty) {
      setWarranty(contextProdData.attributes_list.warranty);
    }

    if (contextProdData.attributes_list?.name) {
      localStorage.setItem('prodName', contextProdData.attributes_list.name);
    }
  }, [contextProdData]);

  useEffect(() => {
    if (contextProdData?.category_associated?.length > 0) {
      fetchCategoryIds(
          contextProdData.category_associated
        ).then((response) => {
          if (response) {
            const formattedItems = response.map((category: any) => 
              `${category.name} (ID: ${category.id})`
            );
            setSelectedItems(formattedItems);
          } else {
            setSelectedItems([]);
          }
        })
        .catch((error) => {
          addToast('error', 'Error fetching category Names');
        });
    }
  }, [contextProdData.category_associated]);

  // Product Expiry

  const [prodExpiry, setProdExpiry] = useState<Date | undefined>(
    contextProdData.attributes_list?.pd_expiry_date
      ? new Date(contextProdData.attributes_list?.pd_expiry_date)
      : undefined,
  );

  // Set Product as New
  const [prodAsNewFrom, setProdAsNewFrom] = useState<Date | undefined>(
    contextProdData.attributes_list?.news_from_date
      ? new Date(contextProdData.attributes_list?.news_from_date)
      : undefined,
  );

  const [prodAsNewTo, setProdAsNewTo] = useState<Date | undefined>(
    contextProdData.attributes_list?.news_from_date
      ? new Date(contextProdData.attributes_list?.news_from_date)
      : undefined,
  );

  // Warranty
  const [warranty, setWarranty] = useState<string>(
    contextProdData?.attributes_list?.warranty || '',
  );

  const formatDateString = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  useEffect(() => {
    if (id) {
      if (contextProdData?.status) {
        setStatus(contextProdData.status);
      }
    }
  }, [id, contextProdData]);

  useEffect(() => {
    if (!id) {
      setStatus(true);
      setContextProdData((prevState) => ({
        ...prevState,
        status: true,
      }));
      setContextUpdateProdData((prevState) => ({
        ...prevState,
        status: true,
      }));
    }
  }, []);

  return (
    <Main style={{backgroundColor: '#fff', height: '100%'}}>
      <>
        <Row>
          <Col>
            <Label>Enable Product</Label>
            <Field>
              <Toggle
                checked={status}
                onChange={handleChangeStatus}
                style={{
                  backgroundColor: `${baseTheme.colors.deepBlue}`,
                }}
              >
                <Label hidden>Enable Product</Label>
              </Toggle>
            </Field>
          </Col>
        </Row>
        <Row
          style={{
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Col size={6}>
            <Field>
              <Label>
                Product Type <span style={{ color: 'red' }}>*</span>
              </Label>
              {id ? (
                <Input value={type} disabled />
              ) : (
                <ProductDropdown
                  options={[
                    'Simple Product',
                    'Grouped Product',
                    'Virtual Product',
                  ]}
                  selectedItem={type}
                  inputValue={inputProdType}
                  onSelect={handleSelectProdType}
                  onInputValueChange={handleInputProdTypeChange}
                />
              )}
            </Field>
          </Col>
          {id && (
            <Col>
              <Field>
                <Label>SKU</Label>
                <Input
                  value={contextProdData?.sku}
                  onChange={(e) => {
                    setContextProdData((prevState) => ({
                      ...prevState,
                      sku: e.target.value,
                    }));
                    setContextUpdateProdData((prevState) => ({
                      ...prevState,
                      sku: e.target.value,
                    }));
                  }}
                  disabled
                />
              </Field>
            </Col>
          )}
        </Row>
        <Row
          style={{
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Col>
            <Field>
              <Label>
                Product Name <span style={{ color: 'red' }}>*</span>
              </Label>
              <Input
                value={contextProdData?.attributes_list?.name}
                onChange={(e) => {
                  const value = e.target.value;
                  const trimmedValue = value.trimStart();
                  const wordCount = trimmedValue.split(/\s+/).length;

                  if (wordCount <= 100) {
                    updateContextData(
                      'name',
                      capitalizeFirstLetter(trimmedValue),
                    );
                  }
                }}
                onKeyPress={handleKeyPress}
              />
            </Field>
          </Col>
          <Col>
            <Label>
              Visibility <span style={{ color: 'red' }}>*</span>
            </Label>
            <ProductDropdown
              // label="Visibility *"
              options={
                visibilityList.length > 0 ? [...visibilityList] : ['Loading...']
              }
              selectedItem={visibility}
              inputValue={inputVisibility}
              onSelect={handleSelectVisibility}
              onInputValueChange={handleInputVisibilityChange}
            />
          </Col>
        </Row>
        <Row>
          <Col>
            <Label>Categories</Label>
            <div style={{ position: 'relative' }}>
              <Dropdown
                selectedItems={selectedItems}
                // inputValue={inputValue} - removed
                // onInputValueChange={(value) => { - removed
                //   setInputValue(value);
                //   setCategName(value);
                // }}
                downshiftProps={{ 
                  defaultHighlightedIndex: 0,
                  // Disable input typing
                  isOpen: undefined, // Let dropdown control open state
                  inputValue: "", // Keep input value empty
                  onInputValueChange: () => {} // No-op function to prevent typing
                }}
              >
                <DropField>
                  <DropLabel hidden>Accessibly hidden label</DropLabel>
                  <Multiselect
                    renderItem={({ value, removeValue }: any) => (
                      <Tag>
                        <span>{value}</span>
                        <Tag.Close
                          onClick={() => {
                            setSelectedItems((prevSelectedItems) =>
                              prevSelectedItems.filter(
                                (item) => item !== value,
                              ),
                            );
                          }}
                        />
                      </Tag>
                    )}
                  />
                </DropField>
                <Menu
                  style={{
                    transform: 'translateX(4px)',
                    borderRadius: baseTheme.borderRadii.lg,
                  }}
                >
                  <CategoryAssociated
                    selected={selectedItems}
                    setSelected={setSelectedItems}
                    categoryData={categoryData}
                    setCategoryData={setCategoryData}
                    categoryIds={categoryIds}
                  />
                </Menu>
              </Dropdown>

              {/* <Label
                style={{
                  position: 'absolute',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  right: '40px',
                  zIndex: '1',
                  cursor: 'pointer',
                  padding: '5px',
                  borderRadius: '5px',
                }}
                onClick={() => setIsOpenCategory(!isOpenCategory)}
              >
                Add New Category
              </Label> */}
            </div>
          </Col>
        </Row>
        <Row>
          <Col>
            <>
              {type !== 'Grouped Product' ? (
                <>
                  <Label>
                    Brand <span style={{ color: 'red' }}>*</span>
                  </Label>
                  <ProductDropdown
                    // label="Manufacturer *"
                    options={[...manfacturerList]}
                    selectedItem={manufactur}
                    inputValue={inputManufacturer}
                    onSelect={handleSelectManufacturer}
                    onInputValueChange={handleInputManufacturerChange}
                  />
                </>
              ) : (
                <Field>
                  <Label>Brand</Label>
                  <Input disabled />
                </Field>
              )}
            </>
          </Col>
          <Col>
            {type !== 'Grouped Product' ? (
              <>
                <ProductDropdown
                  label="Country of Origin"
                  options={[...countryList]}
                  selectedItem={country}
                  inputValue={inputCountry}
                  onSelect={handleSelectCountry}
                  onInputValueChange={handleInputCountryChange}
                />
              </>
            ) : (
              <Field>
                <Label>Country of Origin</Label>
                <Input disabled />
              </Field>
            )}
          </Col>
        </Row>
        <Row>
          <Col size={6}>
            <Field>
              <Label>Expiry</Label>
              <Datepicker
                value={prodExpiry}
                minValue={new Date()}
                onChange={(date) => {
                  if (date instanceof Date) {
                    const dateString = formatDateString(date);
                    updateContextData('pd_expiry_date', dateString);
                    setProdExpiry(date);
                  }
                }}
              >
                <MediaInput
                  disabled={contextProdData.type_id === 'grouped'}
                  end={
                    <div
                      style={{
                        alignItems: 'center',
                        display: 'flex',
                        justifyContent: 'center',
                      }}
                    >
                      {prodExpiry ? (
                        <CrossIcon
                          style={{ cursor: 'pointer' }}
                          onClick={() => {
                            setProdExpiry(undefined);
                            updateContextData('pd_expiry_date', null);
                          }}
                        />
                      ) : (
                        <CalenderIcon style={{ cursor: 'pointer' }} />
                      )}
                    </div>
                  }
                />
              </Datepicker>
            </Field>
          </Col>
        </Row>
        {contextProdData.type_id != 'grouped' && (
          <Row>
            <Col size={6}>
              <Field>
                <Label>Weight</Label>
                <MediaInput
                  type="number"
                  value={
                    type != 'Virtual Product'
                      ? contextProdData?.attributes_list?.weight ?? ''
                      : 0
                  }
                  disabled={type === 'Virtual Product'}
                  onChange={(e) => {
                    const value = e.target.value;
                    const parsedValue = parseFloat(value);
                    if (parsedValue > 0) {
                      updateContextData('weight', parsedValue);
                    }
                    if (value === '') {
                      updateContextData('weight', null);
                    }
                  }}
                  end={<>gms</>}
                />
              </Field>
            </Col>
          </Row>
        )}
        <Col>
          <Label>Set product as New from </Label>
        </Col>
        <Row>
          <Col>
            <Datepicker
              value={prodAsNewFrom}
              maxValue={prodExpiry ? new Date(prodExpiry) : undefined}
              onChange={(date) => {
                if (date instanceof Date) {
                  const dateString = formatDateString(date);
                  updateContextData('news_from_date', dateString);
                  setProdAsNewFrom(date);
                }
              }}
            >
              <MediaInput
                end={
                  <div
                    style={{
                      alignItems: 'center',
                      display: 'flex',
                      justifyContent: 'center',
                    }}
                  >
                    {prodAsNewFrom ? (
                      <CrossIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setProdAsNewFrom(undefined);
                          updateContextData('news_from_date', null);
                        }}
                      />
                    ) : (
                      <CalenderIcon style={{ cursor: 'pointer' }} />
                    )}
                  </div>
                }
              />
            </Datepicker>
          </Col>
          to
          <Col>
            <Datepicker
              value={prodAsNewTo}
              maxValue={prodExpiry ? new Date(prodExpiry) : undefined}
              minValue={prodAsNewFrom ? new Date(prodAsNewFrom) : undefined}
              onChange={(date) => {
                if (date instanceof Date) {
                  const dateString = formatDateString(date);
                  updateContextData('news_to_date', dateString);
                  setProdAsNewTo(date);
                }
              }}
            >
              <MediaInput
                end={
                  <div
                    style={{
                      alignItems: 'center',
                      display: 'flex',
                      justifyContent: 'center',
                    }}
                  >
                    {prodAsNewTo ? (
                      <CrossIcon
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setProdAsNewTo(undefined);
                          updateContextData('news_to_date', null);
                        }}
                      />
                    ) : (
                      <CalenderIcon style={{ cursor: 'pointer' }} />
                    )}
                  </div>
                }
              />
            </Datepicker>
          </Col>
        </Row>

        {isOpenCategory && (
          <AddNewCategory
            visible={isOpenCategory}
            setVisible={setIsOpenCategory}
          />
        )}
      </>
    </Main>
  );
};

export default General;
