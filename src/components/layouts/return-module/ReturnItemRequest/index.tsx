import { Button } from '@zendeskgarden/react-buttons';
import { mediaQuery } from '@zendeskgarden/react-theming';
import React, { useEffect, useState } from 'react';
import { baseTheme, colors } from '../../../../themes/theme';
import styled from 'styled-components';
import { useScreenSize } from '../../../../hooks/useScreenSize';
import { Col, Row } from '../../../UI-components/Grid';
import { useNavigate, useParams } from 'react-router-dom';
import General from './General';
import ReturnItem from './ReturnItem';
import Order from './Order';
import ReturnDTO from './ReturnDTO';
import { useQuery } from '@tanstack/react-query';
import useAxios from '../../../../hooks/useAxios';
import routes from '../../../../constants/routes';
import { pageRoutes } from '../../../navigation/RouteConfig';
import {
  useGetReturnItem,
  useGetReturnRequestByOrderId,
} from '../../../../hooks/useQuery';
import { useAuth } from '../../../providers/AuthProvider';

const Tab = styled(Button)<{ isTab: boolean }>`
  border-top: 4px solid ${(p) => (p.isTab ? p.theme.colors.primaryHue : 'none')};
  padding: ${baseTheme.space.md};
  background-color: ${(p) => (p.isTab ? 'white' : colors.tabGrey)};
  ${(p) => mediaQuery('down', 'md', p.theme)} {
    padding: ${baseTheme.space.sm};
    font-size: ${baseTheme.fontSizes.xs};
  }
  color: ${(p) => (p.isTab ? baseTheme.colors.deepBlue : colors.heavyGrey)};
  border-radius: 0;
  font-size: 15px;
  font-weight: 600;
  height: 50px;
  padding: 15px 20px;
`;

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
  background-color: white;
  margin: 16px;
`;

export default ({
  menu,
  orderId,
}: {
  menu: 'general' | 'returnItem' | 'order' | 'returnDto';
  orderId?: string | null;
}) => {
  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Return Item Requests',
      breadcrumbParent: '',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const isSmallScreen = useScreenSize();
  const navigate = useNavigate();
  const [returnOrder, setReturnOrder] = useState<any[]>([]);
  const axios = useAxios();
  const { returnId: returnItemID } = useParams();

  const [returnId, setReturnId] = useState<any>(returnItemID);
  const [orderID, setOrderID] = useState<string>();

  const {
    data: returnItemRequests,
    isLoading: isItemLoading,
    refetch: reloadItemRequest,
  } = useGetReturnItem({ returnId: returnItemID });

  const {
    data: requestDataByOrder,
    isLoading: isOrderLoading,
    refetch: reloadRequestThroughOrderID,
  } = useGetReturnRequestByOrderId({ orderId: orderID });

  useEffect(() => {
    if (returnItemRequests) {
      setReturnOrder(returnItemRequests.items);
      const { order_id } = returnItemRequests;
      setOrderID(order_id);
    }
  }, [returnItemRequests]);

  return (
    <>
      <Container>
        <Row>
          <Col>
            <Row>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_RETURN_ITEM_GENERAL']}/${returnId}`,
                  );
                }}
                isTab={menu === 'general'}
                isNeutral={menu !== 'general'}
                isBasic
              >
                General
              </Tab>
              <Tab
                onClick={() => {
                  navigate(`${pageRoutes['GO_TO_RETURN_ITEM']}/${returnId}`);
                }}
                isTab={menu === 'returnItem'}
                isNeutral={menu !== 'returnItem'}
                isBasic
              >
                Return Items
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_RETURN_ITEM_ORDER']}/${returnId}`,
                  );
                }}
                isTab={menu === 'order'}
                isNeutral={menu !== 'order'}
                isBasic
              >
                Order
              </Tab>
              <Tab
                onClick={() => {
                  navigate(
                    `${pageRoutes['GO_TO_RETURN_ITEM_DTO']}/${returnId}`,
                  );
                }}
                isTab={menu === 'returnDto'}
                isNeutral={menu !== 'returnDto'}
                isBasic
              >
                Return DTO
              </Tab>
            </Row>
          </Col>
        </Row>
        {menu === 'general' && (
          <General
            requestDataByOrder={requestDataByOrder?.results}
            returnItemRequests={returnItemRequests}
            reloadRequestThroughOrderID={reloadRequestThroughOrderID}
            isOrderLoading={isOrderLoading}
          />
        )}
        {menu === 'returnItem' && (
          <ReturnItem
            dataOrder={requestDataByOrder}
            orderID={orderID}
            isOrderLoading={isOrderLoading}
          />
        )}
        {menu === 'order' && orderID && <Order orderId={orderID} />}
        {menu === 'returnDto' && orderID && (
          <ReturnDTO
            isOrderLoading={isOrderLoading}
            returnOrder={returnOrder}
            orderId={orderID}
          />
        )}
      </Container>
    </>
  );
};
