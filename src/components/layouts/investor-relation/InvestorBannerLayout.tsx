import { useEffect, useState } from 'react';
import { baseTheme } from '../../../themes/theme';
import { Card } from '../../UI-components/Card';
import { Field, Input, Label, Textarea } from '@zendeskgarden/react-forms';
import { Col, Row } from '../../UI-components/Grid';
import { Accordion } from '../../UI-components/Accordion';
import GeneralAccordion from '../../accordion/GeneralAccordion';
// import { useMutation } from '@apollo/client';
import GET_INVESTOR_BANNER from '../../../graphql/queries/getInvestorBanner.gql';
import useToast from '../../../hooks/useToast';
import Address from '../../accordion/investor-relaton/banner/Address';
import { Button } from '../../UI-components/Button';
import { Spinner } from '@zendeskgarden/react-loaders';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import krakendPaths from '../../../constants/krakendPaths';

export interface IAddress {
  address: string;
  email: string;
  name: string;
  telephone: string;
  title: string;
  _id: string;
}

export interface IInventorBanner {
  adresses?: IAddress[];
  created_at: string;
  description: string;
  mob_img: string[];
  updated_at: string;
  web_img: string[];
  _id: string;
}

const InvestorBannerLayout = () => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  const addToast = useToast();
  const [results, setResults] = useState<IInventorBanner>();

  // const {} = useQuery(GET_INVESTOR_BANNER, {
  //   fetchPolicy: 'network-only',
  //   variables: {},
  //   onCompleted: (data: any) => {
  //     setResults(data.getInvestorBanner);
  //   },
  // });
  const axios = useAxios();
  const {} = useQuery({
    queryKey: ['get-investors-banner'],
    queryFn: async () => {
      const response: any = await axios.get(
        `${krakendPaths.INVESTORS_URL}/admin-api/v1/investors/banners`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response;
    },
    onError: (err: any) => {
      addToast('error', `${err.message}`);
    },
    onSuccess: (data) => {
      setResults(data);
    },
  });

  const [editedIB, setEditedIB] = useState<IInventorBanner>({
    _id: '',
    created_at: '',
    description: '',
    mob_img: [],
    updated_at: '',
    web_img: [],
  });

  useEffect(() => {
    if (results) {
      setEditedIB({
        _id: results._id,
        created_at: results.created_at,
        description: results.description,
        mob_img: results.mob_img,
        web_img: results.web_img,
        updated_at: results.updated_at,
      });
    }
  }, [results]);

  // const [updateInvestor, { loading: updateLoading }] = useMutation(
  //   UPDATE_INVESTOR_BANNER,
  //   {
  //     onCompleted: (data) => {
  //       addToast('success', 'Updated successfully');
  //     },
  //     onError: (error) => {
  //       addToast('error', error.message);
  //     },
  //   },
  // );
  const queryClient = useQueryClient();
  const { mutate: updateInvestor, isLoading: updateLoading } = useMutation(
    async (obj: any) => {
      console.log(obj);
      const response = await axios.put(
        `${krakendPaths.INVESTORS_URL}/admin-api/v1/investors/banners`,
        obj,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );

      return response.data;
    },
    {
      onError: (err: any) => {
        addToast('error', `${err.message}`);
      },
      onSuccess: () => {
        addToast('success', 'Investor Banner Updated Successfully');
        queryClient.invalidateQueries(['get-investors-banner']);
      },
    },
  );

  const handleSubmit = () => {
    if (results) {
      const updatedInvestor: IInventorBanner = {
        ...editedIB,
      };

      const { _id, created_at, description, mob_img, updated_at, web_img } =
        updatedInvestor;

      console.log('UPdated investore', updatedInvestor);

      if (
        _id.trim() != '' &&
        created_at.trim() != '' &&
        description.trim() != '' &&
        mob_img[0].trim() != '' &&
        updated_at.trim() != '' &&
        web_img[0].trim() != ''
      ) {
        updateInvestor({
          ...updatedInvestor,
        });
      } else {
        addToast('error', 'Please provide Valid value !!');
      }
    }
  };

  const handleInputChange = (prop: string, e: any) => {
    const val = e.target.value;

    if (prop === 'description') {
      setEditedIB((prev) => ({
        ...prev,
        [prop]: val,
      }));
    } else if (prop === 'web_img' || prop === 'mob_img') {
      const updatedArray: string[] = [val];
      setEditedIB((prev) => ({
        ...prev,
        [prop]: updatedArray,
      }));
    }
  };

  return (
    <>
      <Card
        bg="white"
        style={{ padding: baseTheme.space.md, margin: baseTheme.space.md }}
      >
        <Accordion
          level={4}
          isBare
          isAnimated
          expandedSections={expandedSections}
          onChange={(index) => {
            if (expandedSections.includes(index)) {
              setExpandedSections(expandedSections.filter((n) => n !== index));
            } else {
              setExpandedSections([...expandedSections, index]);
            }
          }}
        >
          <GeneralAccordion
            indexing={0}
            title={'Banner and Description'}
            children={
              <>
                <Row>
                  <Col>
                    <Field>
                      <Label>Web Img Url</Label>
                      <Input
                        defaultValue={results?.web_img[0]}
                        onChange={(e) => {
                          handleInputChange('web_img', e);
                        }}
                      />
                    </Field>
                  </Col>
                </Row>
                <Row mt="md">
                  <Col>
                    <Field>
                      <Label>Mobile Img Url</Label>
                      <Input
                        defaultValue={results?.mob_img[0]}
                        onChange={(e) => {
                          handleInputChange('mob_img', e);
                        }}
                      />
                    </Field>
                  </Col>
                </Row>
                <Row mt="md">
                  <Col>
                    <Field>
                      <Label>Description</Label>
                      <Textarea
                        defaultValue={results?.description}
                        minRows={10}
                        onChange={(e) => {
                          handleInputChange('description', e);
                        }}
                      />
                    </Field>
                  </Col>
                </Row>
                <Row mt="md" justifyContent="start">
                  <Col>
                    <Button
                      onClick={() => {
                        handleSubmit();
                      }}
                      isPrimary
                    >
                      {updateLoading ? <Spinner /> : 'Update'}
                    </Button>
                  </Col>
                </Row>
              </>
            }
            expandedSections={expandedSections}
            setExpandedSections={setExpandedSections}
          />

          {results &&
            results.adresses &&
            results.adresses.map((item: IAddress, index: number) => (
              <>
                <GeneralAccordion
                  indexing={index + 1}
                  title={item.title}
                  children={
                    <>
                      <Address data={item} />
                    </>
                  }
                  expandedSections={expandedSections}
                  setExpandedSections={setExpandedSections}
                />
              </>
            ))}
        </Accordion>
      </Card>
    </>
  );
};

export default InvestorBannerLayout;
