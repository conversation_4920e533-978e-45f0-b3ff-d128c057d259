import { Row } from '@zendeskgarden/react-grid';
import { useEffect, useState } from 'react';
import { Accordion } from '../../UI-components/Accordion';
// import { useQuery } from '@apollo/client';
import GET_INVESTOR_RELATION from '../../../graphql/queries/getInvestorRelation.gql';
import GeneralAccordion from '../../accordion/GeneralAccordion';
import BaseAccordion from '../../accordion/investor-relaton/module/BaseAccordion';
import { Col } from '../../UI-components/Grid';
import { useInvestorRelationContext } from '../../../pages/investor-relation/InvestorRelationContext';
import EditModuleModal from '../../modal/investor-relation/main-content/EditModuleModal';
import RemoveModuleModal from '../../modal/investor-relation/main-content/RemoveModuleModal';
import useAxios from '../../../hooks/useAxios';
import { useQuery } from '@tanstack/react-query';
import useToast from '../../../hooks/useToast';
import krakendPaths from '../../../constants/krakendPaths';

export interface IInvestorRelation {
  _id: string;
  module_name: string;
  collapsableItems: ICollapsibleItem[] | null;
  text: string | null;
  list: IList[] | null;
  updated_at: string;
  created_at: string;
  __typename?: string;
}

export interface IList {
  id: string;
  title: string;
  url: string;
  __typename?: string;
}

export interface ICollapsibleItem {
  id: string;
  title: string;
  text: string | null;
  list: IList[] | null;
  __typename?: string;
}

const InvestorRelationLayout = () => {
  const { results, setResults } = useInvestorRelationContext();
  const [expandedSections, setExpandedSections] = useState<number[]>([]);
  const [openEditModal, setOpenEditModal] = useState<any>(null);
  const [openDeleteModal, setOpenDeleteModal] = useState<any>(null);
  const addToast = useToast();
  // const {} = useQuery(GET_INVESTOR_RELATION, {
  //   fetchPolicy: 'network-only',
  //   variables: {},
  //   onCompleted: (data: any) => {
  //     setResults(data.getInvestorRelation);
  //   },
  // });
  const axios = useAxios();
  const { data: investorsData } = useQuery({
    queryKey: ['get-investors'],
    queryFn: async (): Promise<any> => {
      const response: any = await axios.get(
        `${krakendPaths.INVESTORS_URL}/admin-api/v1/investors`,
        {
          // params: {
          //   page: filters.pageNumber,
          //   pageSize: filters?.rowsPerPage,
          // },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
          },
        },
      );
      return response?.investors;
    },
    onError: (err: any) => {
      addToast('error', err?.message);
    },
    onSuccess: (data) => {
      console.log('INV REL DATA');
      setResults(data);
    },
  });

  useEffect(() => {
    console.log('Expanded Section', expandedSections);
  }, [expandedSections]);

  useEffect(() => {
    console.log('Results', results);
  }, [results]);

  const handleEditDelete = (action: string, item: any) => {
    if (action === 'edit') {
      setOpenEditModal(item);
    } else if (action === 'remove') {
      setOpenDeleteModal(item);
    }
  };

  const closeEditModal = () => {
    setOpenEditModal(false);
  };

  const closeDeleteModal = () => {
    setOpenDeleteModal(false);
  };

  return (
    <>
      <Row>
        <Col>
          <Accordion
            level={4}
            isBare
            isAnimated
            expandedSections={expandedSections}
            onChange={(index) => {
              if (expandedSections.includes(index)) {
                setExpandedSections(
                  expandedSections.filter((n) => n !== index),
                );
              } else {
                setExpandedSections([...expandedSections, index]);
              }
            }}
          >
            {results.map((item, index) => (
              <>
                {item.module_name && (
                  <>
                    <GeneralAccordion
                      inAction={true}
                      handleBtnAction={handleEditDelete}
                      item={item}
                      indexing={index}
                      title={item.module_name}
                      children={<BaseAccordion index={3} data={item} />}
                      expandedSections={expandedSections}
                      setExpandedSections={setExpandedSections}
                    />
                  </>
                )}
              </>
            ))}
          </Accordion>
        </Col>
      </Row>
      {openEditModal && (
        <EditModuleModal close={closeEditModal} data={openEditModal} />
      )}
      {openDeleteModal && (
        <RemoveModuleModal
          close={closeDeleteModal}
          listItem={openDeleteModal}
        />
      )}
    </>
  );
};

export default InvestorRelationLayout;
